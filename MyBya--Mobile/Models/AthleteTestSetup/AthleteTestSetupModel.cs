using System;
using System.Text.Json.Serialization;
using MyBya.Models.Common;

namespace MyBya.Models.AthleteTestSetup;

public class AthleteTestSetupModel : IBaseModel
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("creationTime")]
    public DateTime? CreationTime { get; set; }

    [JsonPropertyName("creatorId")]
    public Guid? CreatorId { get; set; }

    [JsonPropertyName("lastModificationTime")]
    public DateTime? LastModificationTime { get; set; }

    [JsonPropertyName("lastModifierId")]
    public Guid? LastModifierId { get; set; }

    [Json<PERSON>ropertyName("testDetailAthleteId")]
    public int? TestDetailAthleteId { get; set; }

    [JsonPropertyName("testCalendarId")]
    public int? TestCalendarId { get; set; }

    [JsonPropertyName("sport")]
    public int? Sport { get; set; }

    [JsonPropertyName("time")]
    public string Time { get; set; }

    [JsonPropertyName("intervalType")]
    public int? IntervalType { get; set; }

    [JsonPropertyName("level")]
    public int? Level { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("updatedAt")]
    public DateTime? UpdatedAt { get; set; }
}
