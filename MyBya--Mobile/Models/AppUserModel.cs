using System;
using MyBya.Models.Common;

namespace MyBya.Models;

public class AppUserModel : IBaseModel
{
    public string Email { get; set; }
    public string ResetPasswordToken { get; set; }
    public DateTime? ResetPasswordSentAt { get; set; }
    public DateTime? RememberCreatedAt { get; set; }
    public int SignInCount { get; set; }
    public DateTime? CurrentSignInAt { get; set; }
    public DateTime? LastSignInAt { get; set; }
    public string CurrentSignInIp { get; set; }
    public string LastSignInIp { get; set; }
    public string ConfirmationToken { get; set; }
    public DateTime? ConfirmedAt { get; set; }
    public DateTime? ConfirmationSentAt { get; set; }
    public string UnconfirmedEmail { get; set; }
    public int FailedAttempts { get; set; }
    public string UnlockToken { get; set; }
    public DateTime? LockedAt { get; set; }
    public int? MemberId { get; set; }
    public bool SetupComplete { get; set; }
    public DateTime? DisableAt { get; set; }
    public DateTime? LastModificationTime { get; set; }
    public int? LastModifierId { get; set; }
    public DateTime CreationTime { get; set; }
    public int? CreatorId { get; set; }
    public int Id { get; set; }
}
