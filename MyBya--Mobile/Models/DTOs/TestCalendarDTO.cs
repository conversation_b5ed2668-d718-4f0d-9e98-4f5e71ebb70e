using System;
using MyBya.Enums;

namespace MyBya.Models.DTOs;

public class TestCalendarDTO : TestCalendar
{
    public int IntervalCount { get; set; }
    public bool MinutesAndSecondsInputsVisible { get; set; }
    public bool MPHInputVisible { get; set; }

    private int actualTimeMinutes;
    public int ActualTimeMinutes 
    {
        get => actualTimeMinutes;
        set => SetProperty(ref actualTimeMinutes, value);
    }

    private int actualTimeSeconds;
    public int ActualTimeSeconds 
    {
        get => actualTimeSeconds;
        set => SetProperty(ref actualTimeSeconds, value);
    }

    private decimal actualPaceMph;
    public decimal ActualPaceMph 
    {
        get => actualPaceMph;
        set => SetProperty(ref actualPaceMph, value);
    }

    private int heartRate;
    public int HeartRate 
    {
        get => heartRate;
        set => SetProperty(ref heartRate, value);
    }

    
    private decimal bLacValue;
    public decimal BLacValue 
    {
        get => bLacValue;
        set => SetProperty(ref bLacValue, value);
    }

    public void SetActualPaceInputVisibility(IntervalTypeEnum intervalType) 
    {
        MinutesAndSecondsInputsVisible = intervalType == IntervalTypeEnum.DistanceBased;
        MPHInputVisible = intervalType == IntervalTypeEnum.TimeBased;
    }
}
