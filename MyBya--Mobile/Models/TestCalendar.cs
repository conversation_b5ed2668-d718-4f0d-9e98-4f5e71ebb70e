using MyBya.Enums;
using MyBya.Models.Common;

namespace MyBya.Models;

public class TestCalendar : IBaseModel
{
    public DateTime Date { get; set; }
    public TimeIndicatorEnum TimeIndicator { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string Location { get; set; }
    public SportEnum Sport { get; set; }
    public ProtocolEnum Protocol { get; set; }
    public IntervalTypeEnum IntervalType { get; set; }
    public int NumberOfInterval { get; set; }
    public int SbtOwnerId { get; set; }
    public int StandardIntervalId { get; set; }
}
