﻿using MyBya.Helpers;
using MyBya.Interfaces;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.TestProtocol;
using Serilog;

namespace MyBya;

public partial class App : Application
{
	public App()
	{
		InitializeComponent();
		InitLogging();
		MainPage = new NavigationPage(new TestProtocolSetupPage(new TestProtocolSetupViewModel()));
	}

	private static void InitLogging()
    {
		ServiceHelper.GetService<ILogService>().Initialize();
    }
}