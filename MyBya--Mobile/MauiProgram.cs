﻿using CommunityToolkit.Maui;
using Microsoft.Extensions.Logging;
using MyBya.Configuration;
using MyBya.DbContext.Handler;
using MyBya.Extensions;

namespace MyBya;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		InitDatabase();
		return Builder();
	}

	private static void InitDatabase()
	{
		DatabaseHandler.Instance.InitDatabase();
	}

	private static MauiApp Builder()
	{
		var builder = AppMauiContext.Instance.Builder;

		builder
			.UseMauiApp<App>()
			.UseMauiCommunityToolkit()
			.AddDependencies()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			});
#if DEBUG
		builder.Logging.AddDebug();
#endif
		return builder.Build();
	}
}