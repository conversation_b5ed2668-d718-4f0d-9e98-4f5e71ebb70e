using System;
using CommunityToolkit.Maui;
using MyBya.AutoMapper;
using MyBya.Interfaces;
using MyBya.Logging;
using MyBya.Services.AppUser;
using MyBya.Services.Athletes;
using MyBya.Services.AthleteTestSetup;
using MyBya.Services.Client;
using MyBya.Services.Members;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.TestProtocol;

namespace MyBya.Extensions;

public static class MauiAppBuilderExtension
{
    public static MauiAppBuilder AddDependencies(this MauiAppBuilder builder)
    {
        Add<PERSON><PERSON>letons(builder);
        AddPages(builder);
        AddViewModels(builder);
        AddServices(builder);
        AddDataAccess(builder);
        AddControls(builder);
        AddAutoMapper(builder);
        AddViews(builder);
        return builder;
    }

    private static void AddViews(MauiAppBuilder builder)
    {
		builder.Services.AddTransient<LoadingView>();
    }

    private static void AddAutoMapper(MauiAppBuilder builder)
    {
        builder.Services.AddAutoMapper(typeof(ConfigurationMapper));
    }

    private static void AddControls(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<Spinner>();
	}

	private static void AddDataAccess(MauiAppBuilder builder)
	{
	}

	private static void AddSingletons(MauiAppBuilder builder)
	{
		builder.Services.AddSingleton<IAlertService, AlertService>();
		builder.Services.AddSingleton<INavigationService, NavigationService>();
	}

	private static void AddPages(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<TestProtocolSetupPage>();
		builder.Services.AddTransient<EnterTestDataPage>();
		builder.Services.AddTransient<BepsScoresPage>();
	}

	private static void AddViewModels(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<TestProtocolSetupViewModel>();
		builder.Services.AddTransient<EnterTestDataViewModel>();
		builder.Services.AddTransient<BepsScoresViewModel>();
	}

	private static void AddServices(MauiAppBuilder builder)
	{
		builder.Services.AddTransient<ILogService, LogService>();
		builder.Services.AddTransient<MembersService>();
		builder.Services.AddTransient<AppUserService>();
		builder.Services.AddTransient<AthletesService>();
		builder.Services.AddTransient<AthleteTestSetupService>();
	}
}
