using System;
using SQLite;

namespace MyBya.DbContext.Handler;

public class DatabaseHandler
{
private static DatabaseHandler? instance;
    public static DatabaseHandler Instance
    {
        get => instance ??= new DatabaseHandler();
    }

    public SQLiteConnection? Connection { get; set; }

    public void InitDatabase()
    {
        CreateConnection();
        CreateTables();
    }

    private void CreateConnection()
    {
        var connection = new SQLiteConnectionString(databasePath: DatabaseConstants.DatabasePath);
        Connection = new SQLiteConnection(connection);
    }

    public void CreateTables()
    {
        IList<Type> tableTypes = new List<Type>
        {
        };

        Connection?.CreateTables(
            CreateFlags.None,
            tableTypes.ToArray()
        );
    }

    public void CloseConnection()
    {
        Connection?.Close();
    }
}
