using System.Linq.Expressions;
using MyBya.DbContext.Handler;
using Serilog;
using SQLite;
using SQLiteException = SQLite.SQLiteException;

namespace MyBya.DataAccess;

public class DataAccessBase<T> where T : new()
{
    private static readonly object _dbLock = new object();

    protected SQLiteConnection Connection
    {
        get 
        {
            lock (_dbLock) 
            {
                return DatabaseHandler.Instance.Connection; 
            }
        }
    }

    public virtual void Delete(T entity)
    {
        lock (_dbLock) 
        {
            try
            {
                Connection.Delete(entity);
            }
            catch (SQLiteException sqlException) 
            {
                Log.Logger.Error(sqlException, sqlException.Message);
                throw;
            }
        }
    }

    public virtual void Insert(T entity)
    {
        lock (_dbLock) 
        {
            try
            {
                Connection.Insert(entity);
            }
            catch (SQLiteException sqlException) 
            {
                Log.Logger.Error(sqlException, sqlException.Message);
                throw;
            }     
        }
    }

    public virtual IList<T> SelectAllItems()
    {
        lock (_dbLock) 
        {
            try
            {
                return Connection
                    .Table<T>()
                    .ToList();
            }
            catch (SQLiteException sqlException) 
            {
                Log.Logger.Error(sqlException, sqlException.Message);
                throw;
            }
        }
    }

    public virtual IList<T> SelectByCriteria(Expression<Func<T, bool>> predicate)
    {
        lock (_dbLock) 
        {
            try
            {
                return Connection
                    .Table<T>()
                    .Where(predicate)
                    .ToList();
            }
            catch (SQLiteException sqlException) 
            {
                Log.Logger.Error(sqlException, sqlException.Message);
                throw;
            }
        }
    }

    public virtual void Update(T entity)
    {
        lock (_dbLock) 
        {
            try
            {
                int execution = Connection.Update(entity);
            }
            catch (SQLiteException sqlException) 
            {
                Log.Logger.Error(sqlException, sqlException.Message);
                throw;
            }     
        }
    }

    public virtual void Clear() 
    {
        lock (_dbLock) 
        {
            try
            {
                var items = Connection.Table<T>().ToList();
    
                foreach (var item in items) 
                    Connection.Delete(item);
            }
            catch (SQLiteException sqlException) 
            {
                Log.Logger.Error(sqlException, sqlException.Message);
                throw;
            }
        }
    }
}
