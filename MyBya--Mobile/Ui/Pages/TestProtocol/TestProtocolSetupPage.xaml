<?xml version="1.0" encoding="utf-8" ?>
<local:BaseTestProtocolPage 
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        x:Class="MyBya.Ui.Pages.TestProtocol.TestProtocolSetupPage"
        xmlns:local="clr-namespace:MyBya.Ui.Pages.Common.TestProtocol"
        xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
        x:TypeArguments="viewModels:TestProtocolSetupViewModel"
        xmlns:views="clr-namespace:MyBya.Ui.Views"
        xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
        Title="TestProtocolSetupPage">
    <Grid
        RowDefinitions="*">
        <views:BaseTestProtocolView
            TitleText="Hi, {name}">
            <views:BaseTestProtocolView.MainContent>
                <Label
                    TextColor="White"
                    FontSize="18"
                    Text="We provide comprehensive, data-driven insights to create tailored training regimens that maximize efficiency, improve outcomes and transform athletic potential."/>
                <Grid
                    RowDefinitions="*, *">
                    <VerticalStackLayout
                        Spacing="10"
                        Margin="0,20,0,0">
                        <components:Selector
                            SelectedItem="{Binding SelectedAthlete}"
                            ItemSource="{Binding Athletes}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Select Athlete"
                            SelectorBackgroundColor="White"/>
                        <components:Selector
                            SelectedItem="{Binding SelectedSport}"
                            ItemSource="{Binding Sports}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Sports"
                            SelectorBackgroundColor="White"/>
                        <components:Selector
                            SelectedItem="{Binding SelectedLevel}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Level"
                            SelectorBackgroundColor="White"
                            ItemSource="{Binding Levels}"/>
                        <components:LabelEntry
                            TextChanged="LabelEntry_TextChanged"
                            Placeholder="00:00"
                            EntryText="{Binding RunPace, Mode=TwoWay}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Run Pace"/>
                        <components:Selector
                            SelectedItem="{Binding SelectedTrackOrTreadmill}"
                            ItemSource="{Binding TrackOrTreadmillOptions}"
                            TitleFontSize="16"
                            TitleTextColor="White"
                            TitleText="Track/Treadmill"
                            SelectorBackgroundColor="White"/>
                    </VerticalStackLayout>
                </Grid>
            </views:BaseTestProtocolView.MainContent>
        </views:BaseTestProtocolView>
        <Button
            Grid.Row="1"
            VerticalOptions="End"
            Margin="0,0,0,10"
            CornerRadius="12"
            Text="CONTINUE"
            FontSize="16"
            BackgroundColor="#FF7FE8"
            HeightRequest="50"
            WidthRequest="150"
            Command="{Binding ContinueCommand}"/>
    </Grid>
</local:BaseTestProtocolPage>