<?xml version="1.0" encoding="utf-8" ?>
<local:BaseTestProtocolPage
            xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
            xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
            x:Class="MyBya.Ui.Pages.TestProtocol.EnterTestDataPage"
            xmlns:local="clr-namespace:MyBya.Ui.Pages.Common.TestProtocol"
            xmlns:viewModels="clr-namespace:MyBya.Ui.ViewModels.TestProtocol"
            xmlns:views="clr-namespace:MyBya.Ui.Views"
            x:TypeArguments="viewModels:EnterTestDataViewModel"
            xmlns:components="clr-namespace:MyBya.Ui.Views.Components"
            Title="EnterTestDataPage">
    <Grid
        RowDefinitions="*">
        <views:BaseTestProtocolView
            TitleText="Enter Test Data">
            <views:BaseTestProtocolView.MainContent>
                <Label
                    TextColor="White"
                    FontSize="18"
                    Text="Please fill out your test data"/>
                <Grid
                    Margin="0,10,0,0"
                    ColumnDefinitions="*"
                    RowDefinitions="*">
                    <Border
                        Stroke="Transparent"
                        BackgroundColor="White">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="5"/>
                        </Border.StrokeShape>
                        <VerticalStackLayout>
                            <Image
                                Scale="1.2"
                                Source="enter_test_data_rouding_header.png"/>
                                <VerticalStackLayout
                                    Padding="10">
                                    <Label
                                        Text="Test Name"
                                        FontSize="20"
                                        TextColor="#FF7FE8"/>
                                    <Grid
                                        ColumnSpacing="10"
                                        ColumnDefinitions="2*, 2*, 2*, 2*"
                                        Margin="0,10,0,0">
                                        <Label
                                            FontSize="17"
                                            Text="400m Stages"/>
                                        <Label
                                            Grid.Column="1"
                                            FontSize="17"
                                            Text="Actual Pace"/>
                                        <Label
                                            Grid.Column="2"
                                            FontSize="17">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <Span Text="HR"/>
                                                    <Span Text=" (BPM)" TextColor="#ABABAB"/>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                        <Label
                                            Grid.Column="3"
                                            FontSize="17">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <Span Text="BLac"/>
                                                    <Span Text="(mmal/L)" TextColor="#ABABAB"/>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </Grid>
                                    <CollectionView
                                        Margin="0,10,0,0"
                                        ItemsSource="{Binding Items}">
                                        <CollectionView.ItemTemplate>
                                            <DataTemplate>
                                                <Grid
                                                    ColumnSpacing="10"
                                                    RowDefinitions="Auto"
                                                    ColumnDefinitions="2*, 2*, 2*, 2*"
                                                    Margin="0,25,0,0">
                                                    <Label
                                                        VerticalTextAlignment="Center"
                                                        Margin="20,0,0,0"
                                                        Text="{Binding IntervalCount}"/>
                                                    <HorizontalStackLayout
                                                        HorizontalOptions="Start"
                                                        IsVisible="{Binding MinutesAndSecondsInputsVisible}"
                                                        Spacing="3"
                                                        Margin="-30,0,0,0"
                                                        Grid.Column="1">
                                                        <components:CEntry
                                                            Keyboard="Numeric"
                                                            WidthRequest="50"
                                                            BorderColor="Black"
                                                            HorizontalTextAlignment="Center"
                                                            Text="{Binding ActualTimeMinutes}">
                                                            <components:CEntry.HeightRequest>
                                                                <OnPlatform x:TypeArguments="x:Double">
                                                                    <On Platform="iOS" Value="30"/>
                                                                    <On Platform="Android" Value="35"/>
                                                                </OnPlatform>
                                                            </components:CEntry.HeightRequest>
                                                        </components:CEntry>
                                                        <Label
                                                            VerticalTextAlignment="Center"
                                                            Text=":"/>
                                                        <components:CEntry
                                                            Keyboard="Numeric"
                                                            WidthRequest="50"
                                                            BorderColor="Black"
                                                            HorizontalTextAlignment="Center"
                                                            Text="{Binding ActualTimeSeconds}">
                                                            <components:CEntry.HeightRequest>
                                                                <OnPlatform x:TypeArguments="x:Double">
                                                                    <On Platform="iOS" Value="30"/>
                                                                    <On Platform="Android" Value="35"/>
                                                                </OnPlatform>
                                                            </components:CEntry.HeightRequest>
                                                        </components:CEntry>
                                                    </HorizontalStackLayout>
                                                    <components:CEntry
                                                        HorizontalOptions="Start"
                                                        IsVisible="{Binding MPHInputVisible}"
                                                        Keyboard="Numeric"
                                                        Grid.Column="1"
                                                        WidthRequest="60"
                                                        BorderColor="Black" 
                                                        HorizontalTextAlignment="Center"
                                                        Text="{Binding ActualPaceMph}">
                                                        <components:CEntry.HeightRequest>
                                                            <OnPlatform x:TypeArguments="x:Double">
                                                                <On Platform="iOS" Value="28"/>
                                                                <On Platform="Android" Value="35"/>
                                                            </OnPlatform>
                                                        </components:CEntry.HeightRequest>
                                                    </components:CEntry>
                                                    <components:CEntry
                                                        Keyboard="Numeric"
                                                        Grid.Column="2"
                                                        WidthRequest="60"
                                                        BorderColor="Black" 
                                                        HorizontalTextAlignment="Center"
                                                        Text="{Binding HeartRate}">
                                                        <components:CEntry.HeightRequest>
                                                            <OnPlatform x:TypeArguments="x:Double">
                                                                <On Platform="iOS" Value="28"/>
                                                                <On Platform="Android" Value="35"/>
                                                            </OnPlatform>
                                                        </components:CEntry.HeightRequest>
                                                    </components:CEntry>
                                                    <components:CEntry
                                                        Keyboard="Numeric"
                                                        Grid.Column="3"
                                                        WidthRequest="60"
                                                        BorderColor="Black" 
                                                        HorizontalTextAlignment="Center"
                                                        Text="{Binding BLacValue}">
                                                        <components:CEntry.HeightRequest>
                                                            <OnPlatform x:TypeArguments="x:Double">
                                                                <On Platform="iOS" Value="28"/>
                                                                <On Platform="Android" Value="35"/>
                                                            </OnPlatform>
                                                            </components:CEntry.HeightRequest>
                                                        </components:CEntry>
                                                    </Grid>
                                                </DataTemplate>
                                            </CollectionView.ItemTemplate>
                                        </CollectionView>
                                        <Button
                                            Margin="0,15,0,0"
                                            FontSize="18"
                                            CornerRadius="12"
                                            BackgroundColor="#483778"
                                            HeightRequest="40"
                                            WidthRequest="150"
                                            Command="{Binding SubmitCommand}"
                                            Text="SUBMIT"/>
                            </VerticalStackLayout>
                        </VerticalStackLayout>
                    </Border>
                </Grid>
            </views:BaseTestProtocolView.MainContent>
        </views:BaseTestProtocolView>
    </Grid> 
</local:BaseTestProtocolPage>