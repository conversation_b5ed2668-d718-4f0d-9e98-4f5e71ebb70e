using MyBya.Ui.Pages.Common.TestProtocol;
using MyBya.Ui.ViewModels.TestProtocol;

namespace MyBya.Ui.Pages.TestProtocol;

public partial class TestProtocolSetupPage : BaseTestProtocolPage<TestProtocolSetupViewModel>
{
	public TestProtocolSetupPage(TestProtocolSetupViewModel viewModel)
	{
		InitializeComponent();
		BindingContext = viewModel;
	}

    private void LabelEntry_TextChanged(object sender, TextChangedEventArgs e)
    {
		var entry = sender as Entry;

		if (entry != null)
		{
			string text = entry.Text;
			
			text = string.Concat(text.Where(char.IsDigit));
			
			if (text.Length > 2)
			{
				text = text.Insert(2, ":");
			}

			if (text.Length > 5)
			{
				text = text.Substring(0, 5);
			}

			entry.Text = text;
		}
    }
}