using System;
using CommunityToolkit.Maui.Behaviors;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.Pages.Common.TestProtocol;

public abstract class BaseTestProtocolPage<T> : BaseContentPage<T> where T : ViewModelBase
{
    protected override void SetBackGroundColor()
    {
        BackgroundColor = Color.FromArgb("#352859");
    }

    protected override void SetStatusBarStyle()
    {
        try
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                var statusBarBehavior = new StatusBarBehavior();
                statusBarBehavior.StatusBarStyle = CommunityToolkit.Maui.Core.StatusBarStyle.LightContent;
                statusBarBehavior.StatusBarColor = Color.FromArgb("#352859");
                Behaviors.Add(statusBarBehavior);
            });
        }
        catch (Exception ex)
        {
            Log.Logger.Error("Error constructing base content page: " + ex.ToString());
        }
    }
}
