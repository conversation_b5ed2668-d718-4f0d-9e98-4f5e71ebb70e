using System.Windows.Input;
using MyBya.Enums;
using MyBya.Models;
using MyBya.Models.AthleteTestSetup;
using MyBya.Models.DTOs;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class EnterTestDataViewModel : ListViewModelBase<TestCalendarDTO>
{
    private AthleteTestSetupModel? CurrentTestSetup {get; set;}
    
    public ICommand SubmitCommand {get; set;}

    public EnterTestDataViewModel()
    {
        SubmitCommand = new SyncedCommand(Submit);
    }

    public async Task Submit()
    {
        await _navigationService.NavigateToModal<LoadingView>();
        await Task.Delay(1000);
        await _navigationService.CloseModal();
        await _navigationService.NavigateToPage<BepsScoresPage>();
    }

    public override Task OnAppearing()
    {
        var testCalendar = new TestCalendar
        {
            Date = new DateTime(2025, 4, 15),
            TimeIndicator = 0,
            CreatedAt = DateTime.UtcNow.AddDays(-5),
            UpdatedAt = DateTime.UtcNow,
            Location = "São Paulo - Estádio Municipal",
            Sport = 0,
            Protocol = 0,
            IntervalType = IntervalTypeEnum.TimeBased,
            NumberOfInterval = 8,
            SbtOwnerId = 101,
            StandardIntervalId = 3
        };
        
        for (int i = 0; i < testCalendar.NumberOfInterval; i++)
        {
            var model = new TestCalendarDTO
            {   
                IntervalCount = i + 1
            };

            model.SetActualPaceInputVisibility(testCalendar.IntervalType);
            
            Items.Add(model);
        }

        return base.OnAppearing();
    }

    public override Task OnNavigatingTo(object? parameter)
    {
        CurrentTestSetup = (AthleteTestSetupModel?)parameter;
        return base.OnNavigatingTo(parameter);
    }
}
