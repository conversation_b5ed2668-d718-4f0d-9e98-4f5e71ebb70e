using System.Windows.Input;
using MyBya.Enums;
using MyBya.Helpers;
using MyBya.Models;
using MyBya.Models.Athletes;
using MyBya.Models.AthleteTestSetup;
using MyBya.Models.DTOs;
using MyBya.Models.Members;
using MyBya.Services.AppUser;
using MyBya.Services.Athletes;
using MyBya.Services.AthleteTestSetup;
using MyBya.Services.Entities.AthleteTestSetup;
using MyBya.Services.Members;
using MyBya.Ui.Controls;
using MyBya.Ui.Pages.TestProtocol;
using MyBya.Ui.ViewModels.Common;
using Serilog;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class TestProtocolSetupViewModel : ViewModelBase
{   
    private List<string> athletes;
    public List<string> Athletes
    {
        get => athletes;
        set => SetProperty(ref athletes, value);
    }

    private List<SportEnum> sports;
    public List<SportEnum> Sports
    {
        get => sports;
        set => SetProperty(ref sports, value);
    }

    private List<RunningModeEnum> trackOrTreadmillOptions;
    public List<RunningModeEnum> TrackOrTreadmillOptions
    {
        get => trackOrTreadmillOptions;
        set => SetProperty(ref trackOrTreadmillOptions, value);
    }

    private List<LevelEnum> levels;
    public List<LevelEnum> Levels
    {
        get => levels;
        set => SetProperty(ref levels, value);
    }

    private string runPace;
    public string RunPace
    {
        get => runPace;
        set => SetProperty(ref runPace, value);
    }

    private string selectedAthlete;
    public string SelectedAthlete 
    {
        get => selectedAthlete;
        set => SetProperty(ref selectedAthlete, value);
    }

    private SportEnum selectedSport;
    public SportEnum SelectedSport 
    {
        get => selectedSport;
        set => SetProperty(ref selectedSport, value);
    }

    private RunningModeEnum selectedTrackOrTreadmill;
    public RunningModeEnum SelectedTrackOrTreadmill 
    {
        get => selectedTrackOrTreadmill;
        set => SetProperty(ref selectedTrackOrTreadmill, value);
    }

    private LevelEnum selectedLevel;
    public LevelEnum SelectedLevel 
    {
        get => selectedLevel;
        set => SetProperty(ref selectedLevel, value);
    }

    private List<AthleteDTO> _athletes {get; set;}

    public ICommand ContinueCommand{get; set;}

    public TestProtocolSetupViewModel()
    {
        Athletes = new List<string>();

        TrackOrTreadmillOptions = new List<RunningModeEnum>()
        {
            RunningModeEnum.Track,
            RunningModeEnum.Treadmill
        };

        Sports = new List<SportEnum>
        {
            SportEnum.Running,
            SportEnum.Rowing,
            SportEnum.Cycling,
            SportEnum.Swimming
        };

        Levels = new List<LevelEnum>
        {
            LevelEnum.Beginner,
            LevelEnum.Intermediate,
            LevelEnum.Advanced,  
            LevelEnum.Elite
        };

        ContinueCommand = new SyncedCommand(Continue, showLoading: true);
    }

    private async Task Continue()
    {
        int memberId = GetMemberId();

        var athleteTestSetupEntity = new AthleteTestSetupEntity
        {
            user_id = memberId,
            level = (int)SelectedLevel,
            sport = (int)SelectedSport,
            interval_type = (int)SelectedTrackOrTreadmill,
            time = RunPace,
        };

        AthleteTestSetupModel? model = await ServiceHelper
            .GetService<AthleteTestSetupService>()
            .CreateAthleteTest(athleteTestSetupEntity);

        if (model != null)
            await _navigationService.NavigateToPage<EnterTestDataPage>(model);
    }

    private int GetMemberId()
    {
        AthleteDTO athlete = _athletes?
            .FirstOrDefault(x => x.FullName == SelectedAthlete);

        return athlete is null ? 0 : athlete.MemberId;
    }
    
    public override async Task OnAppearing()
    {
        try
        {
            IsBusy = true;

            _athletes = await ServiceHelper
                .GetService<AthletesService>()
                .GetAthletes();

            if (athletes is null)
                return; 

            var names = new List<string>();

            foreach (AthleteDTO athlete in _athletes) 
                names.Add(athlete.FullName);
            
            Athletes = names;
        }
        catch (Exception ex)
        {
            await ShowMessageError();
            Log.Error(ex.Message, ex);
        }
        finally 
        {
            IsBusy = false;
        }
    }
}