using System;
using System.Collections.ObjectModel;
using MyBya.Enums;
using MyBya.Models;
using MyBya.Ui.ViewModels.Common;

namespace MyBya.Ui.ViewModels.TestProtocol;

public class BepsScoresViewModel : ListViewModelBase<BeepScores>
{
    public override Task OnAppearing()
    {
        Items = [
            new (BepsScoresEnum.AF),
            new (BepsScoresEnum.PAC),
            new (BepsScoresEnum.LTCCC),
            new (BepsScoresEnum.ARC3),
            new (BepsScoresEnum.ARC2),
            new (BepsScoresEnum.ARC1),
            new (BepsScoresEnum.ANRC2),
            new (BepsScoresEnum.ANRC1),
        ];

        return base.OnAppearing();
    }
}
