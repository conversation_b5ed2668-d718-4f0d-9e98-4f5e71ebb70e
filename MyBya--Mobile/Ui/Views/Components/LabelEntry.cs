using System;
using MyBya.Interfaces;
using MyBya.Ui.Views.Components.Common;

namespace MyBya.Ui.Views.Components;

public class LabelEntry : BaseComponent, IComponent
{
    private VerticalStackLayout MainContent {get; set;}
    public Entry Entry { get; set;}
    public event EventHandler<TextChangedEventArgs> TextChanged;

    public static readonly BindableProperty EntryTextProperty = BindableProperty.Create(
        nameof(EntryText),
        typeof(string),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay
    );

     public static readonly BindableProperty PlaceholderProperty = BindableProperty.Create(
        nameof(Placeholder),
        typeof(string),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (LabelEntry)bindableObject;
			ctrl.Entry.Placeholder = (string)newValue;
		}
    );

    public string EntryText
	{
		get => (string)GetValue(EntryTextProperty);
		set => SetValue(EntryTextProperty, value);
	}

    public string Placeholder
	{
		get => (string)GetValue(PlaceholderProperty);
		set => SetValue(PlaceholderProperty, value);
	}

    public Keyboard Keyboard
	{
		set
        {
            Entry.Keyboard = value;
        }
	}

    public LabelEntry()
    {
        BuildLayout();
    }

    public void BuildLayout()
    {
        MainContent = new VerticalStackLayout();
        MainContent.Spacing = 10;
        MainContent.Add(Title);

        Entry = new Entry
        {
            BackgroundColor = Colors.White,
            WidthRequest = 250,
            HorizontalOptions = LayoutOptions.Start
        };
        Entry.TextChanged += Entry_TextChanged;
        MainContent.Add(Entry);

        Content = MainContent;
    }

    private void Entry_TextChanged(object sender, TextChangedEventArgs e) 
    {
        EntryText = e.NewTextValue;
        TextChanged?.Invoke(Entry, new TextChangedEventArgs(e.OldTextValue, e.NewTextValue));
    }
}
