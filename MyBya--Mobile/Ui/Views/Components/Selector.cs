using System;
using System.Collections;
using Microsoft.Maui.Controls.Shapes;
using MyBya.Interfaces;
using MyBya.Ui.Views.Components.Common;

namespace MyBya.Ui.Views.Components;

public class Selector : BaseComponent, IComponent
{
    private VerticalStackLayout mainStack;
    private Border selector;
    private Label lblSelectedText;
    private ImageButton ic_arrow;
    private Picker picker;

    public static readonly BindableProperty ItemSourceProperty = BindableProperty.Create(
        nameof(ItemSource),
        typeof(IEnumerable),
        typeof(Selector),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (Selector)bindableObject;
			ctrl.picker.ItemsSource = (IList)newValue;
		}
    );

	public static readonly BindableProperty SelectorBackgroundColorProperty = BindableProperty.Create(
        nameof(SelectorBackgroundColor),
        typeof(Color),
        typeof(Selector),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (Selector)bindableObject;
			ctrl.selector.BackgroundColor = (Color)newValue;
		}
    );

	public static readonly BindableProperty SelectedItemProperty = BindableProperty.Create(
        nameof(SelectedItem),
        typeof(object),
        typeof(Selector),
        default,
        BindingMode.TwoWay
    );

	public IEnumerable ItemSource
	{
		get => (IEnumerable)GetValue(ItemSourceProperty);
		set => SetValue(ItemSourceProperty, value);
	}

	public Color SelectorBackgroundColor
	{
		get => (Color)GetValue(SelectorBackgroundColorProperty);
		set => SetValue(SelectorBackgroundColorProperty, value);
	}

	public object SelectedItem
	{
		get => GetValue(SelectedItemProperty);
		set => SetValue(SelectedItemProperty, value);
	}

    public Selector()
    {
        mainStack = new VerticalStackLayout
        {
            Spacing = 10
        };

        mainStack.Add(Title);
        BuildLayout();
    }

    public void BuildLayout()
    {
        var grid = new Grid();
        var innerGrid = new Grid();

        lblSelectedText = new Label
        {
            IsVisible = false,
            Margin = new Thickness(15, 0, 50, 0),
            VerticalTextAlignment = TextAlignment.Center,
            TextColor = Colors.Black
        };
        innerGrid.Add(lblSelectedText);

        ic_arrow = new ImageButton
        {
            Source = "ic_arrow_down.png",
            Scale = 0.3,
            HorizontalOptions = LayoutOptions.End
        };
        innerGrid.Add(ic_arrow);

        selector = new Border
        {
            Stroke = Colors.Black,
            WidthRequest = 250,
            HorizontalOptions = LayoutOptions.Start,
            InputTransparent = true,
            StrokeShape = new RoundRectangle
            {
                CornerRadius = 4
            }
        };
        selector.Content = innerGrid;
        grid.Add(selector);

        picker = new Picker
        {
            HorizontalOptions = LayoutOptions.Start,
            WidthRequest = 250
        };
        picker.SelectedIndexChanged += SelectedIndex_Changed;
        grid.Add(picker);
        mainStack.Add(grid);

        Content = mainStack;
    }

    private void SelectedIndex_Changed(object? sender, EventArgs e) 
    {
        object selectedItem = ((Picker)sender).SelectedItem;
        SelectedItem = selectedItem is null ? "" : selectedItem;
    }
}
