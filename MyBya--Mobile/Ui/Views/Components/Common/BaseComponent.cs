using System;

namespace MyBya.Ui.Views.Components.Common;

public class BaseComponent : ContentView
{
    protected Label Title {get; set;}

    public static readonly BindableProperty TitleTextProperty = BindableProperty.Create(
        nameof(TitleText),
        typeof(string),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseComponent)bindableObject;
			ctrl.Title.Text = (string)newValue;
		}
    );

    public static readonly BindableProperty TitleFontSizeProperty = BindableProperty.Create(
        nameof(TitleFontSize),
        typeof(double),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseComponent)bindableObject;
			ctrl.Title.FontSize = (double)newValue;
		}
    );

    public static readonly BindableProperty TitleTextColorProperty = BindableProperty.Create(
        nameof(TitleTextColor),
        typeof(Color),
        typeof(BaseComponent),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseComponent)bindableObject;
			ctrl.Title.TextColor = (Color)newValue;
		}
    );

    public double TitleFontSize
	{
		get => (double)GetValue(TitleFontSizeProperty);
		set => SetValue(TitleFontSizeProperty, value);
	}

    public string TitleText
	{
		get => (string)GetValue(TitleTextProperty);
		set => SetValue(TitleTextProperty, value);
	}

    public Color TitleTextColor
	{
		get => (Color)GetValue(TitleTextColorProperty);
		set => SetValue(TitleTextColorProperty, value);
	}

    public BaseComponent()
    {
        Title = new Label();
    }
}
