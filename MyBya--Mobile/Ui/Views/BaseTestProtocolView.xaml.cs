
namespace MyBya.Ui.Views;

public partial class BaseTestProtocolView : ContentView
{
	public static readonly BindableProperty MainContentProperty = BindableProperty.Create(
        nameof(MainContent),
        typeof(StackLayout),
        typeof(BaseTestProtocolView),
        default,
        BindingMode.TwoWay
    );

	public static readonly BindableProperty TitleTextProperty = BindableProperty.Create(
        nameof(TitleText),
        typeof(string),
        typeof(BaseTestProtocolView),
        default,
        BindingMode.TwoWay,
		propertyChanged: (bindableObject, oldValue, newValue) => 
		{
			var ctrl = (BaseTestProtocolView)bindableObject;
			ctrl.title.Text = (string)newValue;
		}
    );

	public string TitleText
	{
		get => (string)GetValue(TitleTextProperty);
		set => SetValue(TitleTextProperty, value);
	}

	public StackLayout MainContent
	{
		get => (StackLayout)GetValue(MainContentProperty);
		set => SetValue(MainContentProperty, value);
	}

	public BaseTestProtocolView()
	{
		InitializeComponent();
		MainContent = new StackLayout();
		mainContent.Add(MainContent);
	}
}