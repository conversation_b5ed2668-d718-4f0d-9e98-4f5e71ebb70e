<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MyBya.Ui.Views.BaseTestProtocolView"
             BackgroundColor="#352859">
    <ScrollView>
        <Grid
            RowDefinitions="Auto, *">
            <Image
                Scale="1.2"
                Margin="0,0,0,-1"
                Source="test_protcol_rouding_header.png"/>
            <Border 
                Grid.Row="1"
                Margin="0,0,0,0"
                VerticalOptions="FillAndExpand"
                BackgroundColor="#483778"
                StrokeThickness="0">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="0"/>
                </Border.StrokeShape>
                <Grid
                    RowDefinitions="Auto, *"
                    Margin="0,10,0,0">
                    <Label
                        x:Name="title"
                        Margin="0,20,0,10"
                        FontSize="24"
                        TextColor="#FF7FE8"
                        HorizontalTextAlignment="Center"/>
                    <VerticalStackLayout
                        Grid.Row="1"
                        Padding="10,0,0,0"
                        x:Name="mainContent">
                        <VerticalStackLayout.Background>
                            <LinearGradientBrush>
                                <GradientStop Color="#483778" Offset="0.44"/>
                                <GradientStop Color="#231B3B" Offset="1.0"/>
                            </LinearGradientBrush>
                        </VerticalStackLayout.Background>
                    </VerticalStackLayout>
                </Grid>
            </Border>
        </Grid>
    </ScrollView>
</ContentView>