using System;
using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Models.Athletes;
using MyBya.Models.DTOs;
using MyBya.Services.Common;
using MyBya.Services.Entities.Athletes;

namespace MyBya.Services.Athletes;

public class AthletesService : BaseService<AthleteModel, AthleteEntity>
{
    public AthletesService()
    {
        apiUrl = string.Concat(ApiKeys.ApiUrl, AppSettingsManager.Settings["AthletesServiceURL"]);
    }

    public async Task<List<AthleteDTO>> GetAthletes(int max = 5) 
    {
        apiUrl = string.Format(apiUrl, "", "0", $"{max}");

        var athletes = new List<AthleteDTO>();
        var response = GetApiResponseList<AthleteModel>(await GetRequest());

        foreach (AthleteModel item in response) 
        {
            var athlete = Mapper.Map<AthleteDTO>(item);
            athlete.SetFullName();
            athletes.Add(athlete);
        }

        return athletes.Where(x => 
            !string.IsNullOrEmpty(x.FirstName) && 
            !string.IsNullOrEmpty(x.LastName)).ToList();
    }
}
