using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Models.Members;
using MyBya.Services.Common;
using MyBya.Services.Entities.Members;

namespace MyBya.Services.Members;

public class MembersService : BaseService<MemberModel, MemberServiceEntity>
{
    public MembersService()
    {
        apiUrl = string.Concat(ApiKeys.ApiUrl, AppSettingsManager.Settings["MemberServiceURL"]);
    }

    public async Task<List<MemberModel>> GetMembers() 
    {
        apiUrl = string.Format(apiUrl, "", "0", "5");

        string json = await GetRequest();
        var response = GetApiResponseList<MemberModel>(json);
        return response;
    }
}
