using System;
using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Models;
using MyBya.Models.Members;
using MyBya.Services.Common;
using MyBya.Services.Entities;
using Newtonsoft.Json;

namespace MyBya.Services.AppUser;

public class AppUserService : BaseService<AppUserModel, AppUserEntity>
{
    public AppUserService()
    {
        apiUrl = string.Concat(ApiKeys.ApiUrl, AppSettingsManager.Settings["AppUserServiceURL"]);
    }

    public async Task<AppUserModel> GetUser(int id) 
    {
        apiUrl = string.Format(apiUrl, id);

        string json = await GetRequest();
        var response = JsonConvert.DeserializeObject<AppUserModel>(json);
        return response;
    }
}
