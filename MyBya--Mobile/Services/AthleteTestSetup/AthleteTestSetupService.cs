using System;
using MyBya.Configuration;
using MyBya.Constants;
using MyBya.Models.AthleteTestSetup;
using MyBya.Services.Common;
using MyBya.Services.Entities.AthleteTestSetup;
using Newtonsoft.Json;

namespace MyBya.Services.AthleteTestSetup;

public class AthleteTestSetupService : BaseService<AthleteTestSetupModel, AthleteTestSetupEntity>
{

    public AthleteTestSetupService()
    {
        apiUrl = string.Concat(ApiKeys.ApiUrl, AppSettingsManager.Settings["TestSetupCalendarURL"]);
    }

    public async Task<AthleteTestSetupModel?> CreateAthleteTest(AthleteTestSetupEntity entity) 
    {
        string response = await PostRequest(JsonConvert.SerializeObject(entity));

        if (!string.IsNullOrEmpty(response))
        {
            AthleteTestSetupModel? model = JsonConvert
                .DeserializeObject<AthleteTestSetupModel>(response);

            return model;
        }

        return null;
    }
}
